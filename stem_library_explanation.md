# Python Stem 库详解

## 什么是 Stem 库？

`stem` 是 Python 中用于与 Tor 网络交互的官方控制库，由 Tor Project 开发和维护。它允许 Python 应用程序以编程方式控制和监控 Tor 进程。

## 核心组件解析

### 1. Signal 类 - 信号控制

`from stem import Signal` 导入的是 Tor 进程可以接受的各种信号枚举。

#### 主要信号类型：

```python
Signal.NEWNYM        # 切换到新电路（新身份）
Signal.RELOAD        # 重新加载 torrc 配置文件  
Signal.SHUTDOWN      # 关闭 Tor，等待 ShutdownWaitLength
Signal.HALT          # 立即退出 Tor
Signal.DUMP          # 转储连接和电路信息到日志
Signal.DEBUG         # 切换日志级别到 DEBUG
Signal.CLEARDNSCACHE # 清除 DNS 缓存
Signal.HEARTBEAT     # 触发心跳日志消息
Signal.DORMANT       # 启用休眠模式（减少CPU和网络使用）
Signal.ACTIVE        # 禁用休眠模式
```

#### 使用示例：
```python
from stem import Signal
from stem.control import Controller

with Controller.from_port() as controller:
    controller.authenticate()
    controller.signal(Signal.NEWNYM)  # 切换身份
```

### 2. Controller 类 - 控制器

`from stem.control import Controller` 导入的是与 Tor 控制端口通信的主要类。

#### 主要功能：

**连接和认证：**
```python
# 连接到默认端口 9051
controller = Controller.from_port()

# 连接到指定端口
controller = Controller.from_port(port=9050)

# 认证（通常使用密码或cookie）
controller.authenticate()
```

**电路管理：**
```python
# 获取所有电路
circuits = controller.get_circuits()

# 关闭特定电路
controller.close_circuit(circuit_id)

# 创建新电路
controller.new_circuit(['relay1', 'relay2', 'relay3'])
```

**配置管理：**
```python
# 设置配置选项
controller.set_conf('ExitNodes', '{us}')
controller.set_conf('EntryNodes', '{gb},{de}')

# 获取配置选项
exit_policy = controller.get_conf('ExitPolicy')

# 重置配置
controller.reset_conf('ExitNodes')
```

**事件监听：**
```python
def circuit_event(event):
    print(f"电路 {event.id} 状态: {event.status}")

controller.add_event_listener(circuit_event, EventType.CIRC)
```

**流量控制：**
```python
# 获取流信息
streams = controller.get_streams()

# 关闭流
controller.close_stream(stream_id)

# 重定向流
controller.attach_stream(stream_id, circuit_id)
```

## 更多高级功能

### 3. 描述符操作
```python
# 获取网络状态
network_status = controller.get_network_statuses()

# 获取服务器描述符
server_desc = controller.get_server_descriptors()

# 获取微描述符
microdesc = controller.get_microdescriptors()
```

### 4. 隐藏服务管理
```python
# 创建隐藏服务
response = controller.create_ephemeral_hidden_service(
    {80: 8080}, 
    await_publication=True
)
service_id = response.service_id

# 删除隐藏服务
controller.remove_ephemeral_hidden_service(service_id)
```

### 5. 带宽和统计信息
```python
# 获取带宽使用情况
bw_events = controller.get_info('bw-event-cache')

# 获取 Tor 版本
tor_version = controller.get_version()

# 获取运行时间
uptime = controller.get_info('uptime')
```

### 6. 地理位置和节点选择
```python
# 按国家选择出口节点
controller.set_conf('ExitNodes', '{us},{ca},{gb}')

# 排除特定国家
controller.set_conf('ExcludeExitNodes', '{cn},{ru}')

# 严格节点选择
controller.set_conf('StrictNodes', '1')
```

## 实际应用场景

### 1. 自动化身份切换
```python
import time
from stem import Signal
from stem.control import Controller

def rotate_identity():
    with Controller.from_port() as controller:
        controller.authenticate()
        controller.signal(Signal.NEWNYM)
        time.sleep(controller.get_newnym_wait())

# 每10分钟切换一次身份
while True:
    rotate_identity()
    time.sleep(600)
```

### 2. 监控 Tor 状态
```python
def monitor_tor():
    with Controller.from_port() as controller:
        controller.authenticate()
        
        # 获取电路信息
        circuits = controller.get_circuits()
        print(f"活跃电路数量: {len(circuits)}")
        
        # 获取流信息
        streams = controller.get_streams()
        print(f"活跃流数量: {len(streams)}")
        
        # 获取带宽使用
        bytes_read = controller.get_info("traffic/read")
        bytes_written = controller.get_info("traffic/written")
        print(f"读取: {bytes_read} 字节, 写入: {bytes_written} 字节")
```

### 3. 动态配置管理
```python
def configure_tor_for_region(country_codes):
    with Controller.from_port() as controller:
        controller.authenticate()
        
        # 设置出口节点
        exit_nodes = '{' + '},{'.join(country_codes) + '}'
        controller.set_conf('ExitNodes', exit_nodes)
        
        # 强制使用新配置
        controller.signal(Signal.NEWNYM)
        
        print(f"已配置出口节点: {exit_nodes}")

# 使用示例
configure_tor_for_region(['us', 'ca', 'gb'])
```

## 错误处理

```python
from stem import ControllerError, SocketError

try:
    with Controller.from_port() as controller:
        controller.authenticate()
        controller.signal(Signal.NEWNYM)
except SocketError:
    print("无法连接到 Tor 控制端口")
except ControllerError as e:
    print(f"控制器错误: {e}")
```

## 总结

Stem 库提供了完整的 Tor 控制功能：
- **Signal**: 发送各种控制信号给 Tor 进程
- **Controller**: 主要的控制接口，管理连接、电路、配置等
- **事件系统**: 监听 Tor 的各种状态变化
- **描述符访问**: 获取网络拓扑和节点信息
- **隐藏服务**: 创建和管理洋葱服务

这使得开发者可以构建复杂的 Tor 应用程序，实现自动化的隐私保护和网络路由控制。
