# SwitchTor 使用说明

## 功能特性

- ✅ 支持 print 语句输出详细操作信息
- ✅ 支持参数化配置 ExitNodes 和 EntryNodes 区域
- ✅ 支持自定义 Tor 控制端口配置
- ✅ 支持 DNS 缓存清除功能
- ✅ 支持全局变量配置（无需命令行参数）
- ✅ 倒计时显示让等待过程更直观
- ✅ 改进的错误处理和重试机制

## 配置方式

### 方式1: 全局变量配置（推荐）

直接修改 `switchtor.py` 文件中的全局变量：

```python
# 全局配置变量
DEFAULT_EXIT_NODES = "{us},{ca}"  # 出口节点区域
DEFAULT_ENTRY_NODES = "{us},{ca}"  # 入口节点区域
DEFAULT_CONTROL_PORT = 9051       # Tor控制端口: 9050, 9051 或其他
CLEAR_DNS_CACHE = True            # 是否清除DNS缓存
```

配置后直接运行：
```bash
python switchtor.py
```

### 方式2: 命令行参数（临时覆盖）

命令行参数会覆盖全局变量设置：
```bash
python switchtor.py --exit-nodes "{gb}" --control-port 9050
```

**优先级**: 命令行参数 > 全局变量 > 默认值

## 基本用法

### 1. 默认配置运行
```bash
python switchtor.py
```

### 2. 指定控制端口
```bash
# 使用9050端口
python switchtor.py --control-port 9050

# 使用9051端口
python switchtor.py --control-port 9051

# 使用自定义端口
python switchtor.py --control-port 9052
```

### 3. DNS 缓存控制
```bash
# 清除DNS缓存（默认行为）
python switchtor.py --clear-dns

# 不清除DNS缓存
python switchtor.py --no-clear-dns
```

### 4. 指定区域节点

#### 美国节点
```bash
python switchtor.py --exit-nodes "{us}" --entry-nodes "{us}"
```

#### 欧洲节点
```bash
python switchtor.py --exit-nodes "{gb},{de},{fr}" --entry-nodes "{gb},{de},{fr}"
```

#### 亚洲节点
```bash
python switchtor.py --exit-nodes "{jp},{sg},{hk}" --entry-nodes "{jp},{sg},{hk}"
```

### 5. 组合使用
```bash
# 指定美国出口节点，9051端口，清除DNS
python switchtor.py --exit-nodes "{us}" --control-port 9051 --clear-dns

# 指定欧洲节点，9050端口，不清除DNS
python switchtor.py --exit-nodes "{gb},{de}" --entry-nodes "{gb},{de}" --control-port 9050 --no-clear-dns
```

## 常用国家代码

| 国家/地区 | 代码 | 示例 |
|----------|------|------|
| 美国 | us | {us} |
| 英国 | gb | {gb} |
| 德国 | de | {de} |
| 法国 | fr | {fr} |
| 日本 | jp | {jp} |
| 新加坡 | sg | {sg} |
| 香港 | hk | {hk} |
| 加拿大 | ca | {ca} |
| 澳大利亚 | au | {au} |
| 荷兰 | nl | {nl} |

## 输出示例

```
=== Tor身份切换工具 ===
配置来源: 全局变量
出口节点区域: {us}
控制端口: 9051
清除DNS缓存: 是
=========================
开始切换Tor身份 (端口: 9051)...
连接到Tor控制端口9051...
认证成功
设置出口节点区域: {us}
找到 3 个电路
成功关闭 3 个电路
清除DNS缓存...
发送NEWNYM信号...
等待推荐时间(10秒): 10秒...9秒...8秒...7秒...6秒...5秒...4秒...3秒...2秒...1秒 完成!
额外等待确保电路建立: 5秒...4秒...3秒...2秒...1秒 完成!
端口9051 Tor身份切换完成

✅ 所有操作完成!
```

## 注意事项

1. 确保 Tor 服务正在运行
2. 确保控制端口已启用并可访问
3. 某些区域的节点可能较少，切换可能需要更长时间
4. 使用多个区域代码时用逗号分隔，如: `{us},{ca},{gb}`
5. DNS 缓存清除有助于确保使用新的网络路径
6. 可以通过修改 torrc 文件设置 `ControlPort` 来指定控制端口

## torrc 配置示例

在 torrc 文件中添加：
```
# 启用控制端口
ControlPort 9051

# 可选：设置控制密码
HashedControlPassword 16:872860B76453A77D60CA2BB8C1A7042072093276A3D701AD684053EC4C

# 可选：允许本地连接
ControlListenAddress 127.0.0.1
```
