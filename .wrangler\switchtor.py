
import secrets
import time
import argparse
import sys
from stem import Signal
from stem.control import Controller
from tenacity import retry

# 全局配置变量 - 可直接修改这里的值
DEFAULT_EXIT_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_ENTRY_NODES = None  # 例如: "{us},{ca},{gb}" 或 "{jp},{sg}"
DEFAULT_PORT = "random"  # 选项: "9050", "9051", "both", "random"

# 常用配置示例（取消注释使用）:
# DEFAULT_EXIT_NODES = "{us}"          # 美国出口
# DEFAULT_ENTRY_NODES = "{us}"         # 美国入口
# DEFAULT_PORT = "9051"                # 固定9051端口

# DEFAULT_EXIT_NODES = "{gb},{de},{fr}"  # 欧洲出口
# DEFAULT_ENTRY_NODES = "{gb},{de},{fr}" # 欧洲入口
# DEFAULT_PORT = "both"                   # 切换两个端口

def countdown_sleep(seconds, message="等待中"):
    """带倒计时显示的睡眠函数"""
    print(f"{message}: ", end="", flush=True)
    for i in range(int(seconds), 0, -1):
        print(f"{i}秒", end="", flush=True)
        time.sleep(1)
        if i > 1:
            print("...", end="", flush=True)
    print(" 完成!")

    # 处理小数部分
    remaining = seconds - int(seconds)
    if remaining > 0:
        time.sleep(remaining)

@retry
def switch_proxy(exit_nodes=None, entry_nodes=None):
    """切换默认端口Tor身份 - 修复版"""
    print("开始切换默认端口(9050) Tor身份...")

    with Controller.from_port() as controller:
        print("连接到Tor控制端口9050...")
        controller.authenticate()
        print("认证成功")

        # 配置区域节点
        if exit_nodes:
            print(f"设置出口节点区域: {exit_nodes}")
            controller.set_conf('ExitNodes', exit_nodes)

        if entry_nodes:
            print(f"设置入口节点区域: {entry_nodes}")
            controller.set_conf('EntryNodes', entry_nodes)

        # 关键修复1: 关闭现有电路
        circuits = controller.get_circuits()
        print(f"找到 {len(circuits)} 个电路")
        closed_count = 0
        for circuit in circuits:
            if circuit.status == 'BUILT':
                try:
                    controller.close_circuit(circuit.id)
                    closed_count += 1
                except Exception as e:
                    print(f"关闭电路 {circuit.id} 失败: {e}")
        print(f"成功关闭 {closed_count} 个电路")

        # 关键修复2: 发送NEWNYM信号
        print("发送NEWNYM信号...")
        controller.signal(Signal.NEWNYM)

        # 关键修复3: 必须等待推荐时间
        wait_time = controller.get_newnym_wait()
        if wait_time > 0:
            countdown_sleep(wait_time, f"等待推荐时间({wait_time}秒)")
        else:
            print("无需等待推荐时间")

        # 关键修复4: 额外等待确保电路建立
        countdown_sleep(5, "额外等待确保电路建立")
        print("默认端口Tor身份切换完成")

@retry
def switch_tor(exit_nodes=None, entry_nodes=None):
    """切换9051端口Tor身份 - 修复版"""
    print("开始切换9051端口 Tor身份...")

    with Controller.from_port(port=9051) as controller:
        print("连接到Tor控制端口9051...")
        controller.authenticate()
        print("认证成功")

        # 配置区域节点
        if exit_nodes:
            print(f"设置出口节点区域: {exit_nodes}")
            controller.set_conf('ExitNodes', exit_nodes)

        if entry_nodes:
            print(f"设置入口节点区域: {entry_nodes}")
            controller.set_conf('EntryNodes', entry_nodes)

        # 关键修复1: 关闭现有电路
        circuits = controller.get_circuits()
        print(f"找到 {len(circuits)} 个电路")
        closed_count = 0
        for circuit in circuits:
            if circuit.status == 'BUILT':
                try:
                    controller.close_circuit(circuit.id)
                    closed_count += 1
                except Exception as e:
                    print(f"关闭电路 {circuit.id} 失败: {e}")
        print(f"成功关闭 {closed_count} 个电路")

        # 关键修复2: 发送NEWNYM信号
        print("发送NEWNYM信号...")
        controller.signal(Signal.NEWNYM)

        # 关键修复3: 必须等待推荐时间
        wait_time = controller.get_newnym_wait()
        if wait_time > 0:
            countdown_sleep(wait_time, f"等待推荐时间({wait_time}秒)")
        else:
            print("无需等待推荐时间")

        # 关键修复4: 额外等待确保电路建立
        countdown_sleep(5, "额外等待确保电路建立")
        print("9051端口Tor身份切换完成")

def main():
    """主函数，支持命令行参数和全局变量"""
    parser = argparse.ArgumentParser(description='Tor身份切换工具')
    parser.add_argument('--exit-nodes', type=str, help='出口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--entry-nodes', type=str, help='入口节点区域 (例如: {us},{ca},{gb})')
    parser.add_argument('--port', type=str, choices=['9050', '9051', 'both', 'random'],
                       help='选择端口')

    args = parser.parse_args()

    # 优先级: 命令行参数 > 全局变量 > 默认值
    exit_nodes = args.exit_nodes or DEFAULT_EXIT_NODES
    entry_nodes = args.entry_nodes or DEFAULT_ENTRY_NODES
    port = args.port or DEFAULT_PORT

    print("=== Tor身份切换工具 ===")
    print(f"配置来源: {'命令行参数' if any([args.exit_nodes, args.entry_nodes, args.port]) else '全局变量'}")
    if exit_nodes:
        print(f"出口节点区域: {exit_nodes}")
    if entry_nodes:
        print(f"入口节点区域: {entry_nodes}")
    print(f"端口选择: {port}")
    print("=" * 25)

    try:
        if port == '9050':
            switch_proxy(exit_nodes, entry_nodes)
        elif port == '9051':
            switch_tor(exit_nodes, entry_nodes)
        elif port == 'both':
            print("切换两个端口...")
            switch_proxy(exit_nodes, entry_nodes)
            switch_tor(exit_nodes, entry_nodes)
        else:  # random
            print("随机选择端口...")
            choice = secrets.choice([
                lambda: switch_proxy(exit_nodes, entry_nodes),
                lambda: switch_tor(exit_nodes, entry_nodes)
            ])
            choice()

        print("\n✅ 所有操作完成!")

    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())